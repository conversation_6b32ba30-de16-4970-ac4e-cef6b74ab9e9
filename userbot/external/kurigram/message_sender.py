import asyncio
import logging
from typing import List, Optional, Dict, Any

logger = logging.getLogger(__name__)


class KurigramMessageSender:
    """
    Kurigram-based message sender that replaces Telethon implementation.
    Handles session management, AUTO folder detection, and message sending.
    """
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.client = None  # Will be initialized in connect_session
        self.session_string: Optional[str] = None
        self.auto_folder_chats: List = []
        
    async def connect_session(self) -> bool:
        """
        Connect to Telegram using Pyrogram client with session data.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Lazy import to avoid event loop issues during Django startup
            from pyrogram import Client
            from userbot.models import Session
            from asgiref.sync import sync_to_async

            # Get session data from database
            session_data = await sync_to_async(
                Session.objects.filter(user_id=self.user_id, is_active=True).first
            )()

            if not session_data:
                logger.error(f"No active session found for user {self.user_id}")
                return False

            # Initialize Pyrogram client with session name and credentials
            self.client = Client(
                name=session_data.session_name,
                api_id=int(session_data.api_id),
                api_hash=session_data.api_hash,
                workdir="sessions/"  # Directory where session files are stored
            )

            # Start the client
            await self.client.start()
            logger.info(f"Successfully connected Pyrogram client for user {self.user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to connect Pyrogram client for user {self.user_id}: {e}")
            return False
    
    async def disconnect_session(self):
        """Disconnect the Kurigram client."""
        if self.client:
            try:
                await self.client.stop()
                logger.info(f"Disconnected Kurigram client for user {self.user_id}")
            except Exception as e:
                logger.error(f"Error disconnecting client for user {self.user_id}: {e}")
            finally:
                self.client = None
    
    async def get_auto_folder_chats(self) -> List:
        """
        Get all chats from the AUTO folder using Kurigram.
        
        Returns:
            List[Chat]: List of chats in the AUTO folder
        """
        if not self.client:
            logger.error("Client not connected")
            return []
            
        try:
            # Get all folders
            folders = await self.client.get_folders()
            
            # Find the AUTO folder
            auto_folder = None
            for folder in folders:
                if folder.name.upper() == "AUTO":
                    auto_folder = folder
                    break
            
            if not auto_folder:
                logger.warning(f"AUTO folder not found for user {self.user_id}")
                return []
            
            # Get chats from the AUTO folder
            auto_chats = []
            
            # Add pinned chats from the folder
            if auto_folder.pinned_chats:
                auto_chats.extend(auto_folder.pinned_chats)
            
            # Add included chats from the folder
            if auto_folder.included_chats:
                auto_chats.extend(auto_folder.included_chats)
            
            # Remove duplicates while preserving order
            seen_ids = set()
            unique_chats = []
            for chat in auto_chats:
                if chat.id not in seen_ids:
                    seen_ids.add(chat.id)
                    unique_chats.append(chat)
            
            self.auto_folder_chats = unique_chats
            logger.info(f"Found {len(unique_chats)} chats in AUTO folder for user {self.user_id}")
            return unique_chats
            
        except Exception as e:
            logger.error(f"Error getting AUTO folder chats for user {self.user_id}: {e}")
            return []
    
    async def send_messages_to_auto_folder(self, text: str) -> Dict[str, Any]:
        """
        Send message to all chats in the AUTO folder.

        Args:
            text (str): Message text to send

        Returns:
            Dict[str, Any]: Result with success status and details
        """
        # Lazy import to avoid event loop issues
        from pyrogram.errors import FloodWait, RPCError

        if not self.client:
            return {
                'success': False,
                'error': 'Client not connected',
                'sent_count': 0,
                'failed_count': 0,
                'details': []
            }
        
        # Get AUTO folder chats if not already loaded
        if not self.auto_folder_chats:
            await self.get_auto_folder_chats()
        
        if not self.auto_folder_chats:
            return {
                'success': False,
                'error': 'No chats found in AUTO folder',
                'sent_count': 0,
                'failed_count': 0,
                'details': []
            }
        
        sent_count = 0
        failed_count = 0
        details = []
        
        for chat in self.auto_folder_chats:
            try:
                # Send message to the chat
                await self.client.send_message(
                    chat_id=chat.id,
                    text=text
                )
                
                sent_count += 1
                details.append({
                    'chat_id': chat.id,
                    'chat_title': getattr(chat, 'title', getattr(chat, 'first_name', 'Unknown')),
                    'status': 'sent'
                })
                
                logger.info(f"Message sent to chat {chat.id} for user {self.user_id}")
                
                # Small delay to avoid flood limits
                await asyncio.sleep(0.5)
                
            except FloodWait as e:
                # Handle flood wait by sleeping and retrying
                logger.warning(f"Flood wait {e.value} seconds for chat {chat.id}, user {self.user_id}")
                await asyncio.sleep(e.value)
                
                try:
                    # Retry sending after flood wait
                    await self.client.send_message(
                        chat_id=chat.id,
                        text=text
                    )
                    sent_count += 1
                    details.append({
                        'chat_id': chat.id,
                        'chat_title': getattr(chat, 'title', getattr(chat, 'first_name', 'Unknown')),
                        'status': 'sent_after_flood_wait'
                    })
                    logger.info(f"Message sent to chat {chat.id} after flood wait for user {self.user_id}")
                    
                except Exception as retry_error:
                    failed_count += 1
                    details.append({
                        'chat_id': chat.id,
                        'chat_title': getattr(chat, 'title', getattr(chat, 'first_name', 'Unknown')),
                        'status': 'failed',
                        'error': str(retry_error)
                    })
                    logger.error(f"Failed to send message to chat {chat.id} after retry for user {self.user_id}: {retry_error}")
                    
            except RPCError as e:
                failed_count += 1
                details.append({
                    'chat_id': chat.id,
                    'chat_title': getattr(chat, 'title', getattr(chat, 'first_name', 'Unknown')),
                    'status': 'failed',
                    'error': f"RPC Error: {e}"
                })
                logger.error(f"RPC error sending message to chat {chat.id} for user {self.user_id}: {e}")
                
            except Exception as e:
                failed_count += 1
                details.append({
                    'chat_id': chat.id,
                    'chat_title': getattr(chat, 'title', getattr(chat, 'first_name', 'Unknown')),
                    'status': 'failed',
                    'error': str(e)
                })
                logger.error(f"Error sending message to chat {chat.id} for user {self.user_id}: {e}")
        
        success = sent_count > 0
        return {
            'success': success,
            'sent_count': sent_count,
            'failed_count': failed_count,
            'total_chats': len(self.auto_folder_chats),
            'details': details
        }
    
    async def _get_session_string(self) -> Optional[str]:
        """
        Get session string for the user from database.
        For now, this will use the session_name to create a file-based session.
        In the future, you may want to implement proper session string storage.

        Returns:
            Optional[str]: Session string if found, None otherwise
        """
        try:
            from userbot.models import Session
            from asgiref.sync import sync_to_async

            # Get session data from database
            session = await sync_to_async(
                Session.objects.filter(user_id=self.user_id, is_active=True).first
            )()

            if not session:
                logger.error(f"No active session found for user {self.user_id}")
                return None

            # For now, we'll use the session_name as the session identifier
            # Kurigram can work with session files or session strings
            # Since we don't have session strings stored, we'll use the session_name
            # and let Kurigram handle the session file
            return session.session_name

        except Exception as e:
            logger.error(f"Error retrieving session for user {self.user_id}: {e}")

        return None


async def send_message_to_auto_folder(user_id: int, text: str) -> Dict[str, Any]:
    """
    Main function to send message to AUTO folder chats using Kurigram.
    This function replaces the Telethon implementation.

    Args:
        user_id (int): Telegram user ID
        text (str): Message text to send

    Returns:
        Dict[str, Any]: Result with success status and details
    """
    sender = KurigramMessageSender(user_id)

    try:
        # Connect to session
        if not await sender.connect_session():
            return {
                'success': False,
                'error': f'Failed to connect session for user_id: {user_id}',
                'results': {}
            }

        # Send messages to AUTO folder
        results = await sender.send_messages_to_auto_folder(text)

        # Format results to match the expected API response
        return {
            'success': results['success'],
            'message': f'Message sending completed for user_id: {user_id}',
            'results': {
                'success': [{'chat_id': detail['chat_id'], 'chat_title': detail['chat_title']}
                           for detail in results['details'] if detail['status'].startswith('sent')],
                'failed': [{'chat_id': detail['chat_id'], 'error': detail.get('error', 'Unknown error')}
                          for detail in results['details'] if detail['status'] == 'failed'],
                'total_chats': results['total_chats'],
                'sent_count': results['sent_count'],
                'failed_count': results['failed_count']
            }
        }

    except Exception as e:
        logger.error(f"Error in send_message_to_auto_folder: {e}")
        return {
            'success': False,
            'error': f'Internal server error: {str(e)}',
            'results': {}
        }

    finally:
        # Always disconnect
        await sender.disconnect_session()
