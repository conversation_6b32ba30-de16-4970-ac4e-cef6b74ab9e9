import asyncio
import logging
from celery import shared_task
from django.utils import timezone

from datetime import timedelta
from userbot.models import Session
from userbot.models import ScheduledMessage
from userbot.external.telethon.message_sender import send_message_to_auto_folder

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def send_scheduled_message(self, user_id, text, scheduled_message_id=None):
    """
    Celery task to send scheduled message to AUTO folder chats
    
    Args:
        user_id (int): Telegram user ID
        text (str): Message text to send
        scheduled_message_id (int, optional): ID of the scheduled message record
    
    Returns:
        dict: Result of the message sending operation
    """
    try:
        logger.info(f"Starting scheduled message task for user_id: {user_id}")

        # Check if session exists and is active
        try:
            _ = Session.objects.get(user_id=user_id, is_active=True)
        except Session.DoesNotExist:
            error_msg = f'No active session found for user_id: {user_id}'
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'user_id': user_id,
                'scheduled_message_id': scheduled_message_id
            }

        # Send message using existing function
        result = asyncio.run(send_message_to_auto_folder(user_id, text))

        # Update scheduled message status if provided
        if scheduled_message_id:
            try:
                scheduled_msg = ScheduledMessage.objects.get(id=scheduled_message_id)
                if result['success']:
                    scheduled_msg.last_sent_at = timezone.now()
                    scheduled_msg.sent_count += 1
                    scheduled_msg.status = 'sent'
                else:
                    scheduled_msg.status = 'failed'
                    scheduled_msg.error_message = result.get('error', 'Unknown error')
                scheduled_msg.save()
            except Exception as e:
                logger.error(f"Error updating scheduled message {scheduled_message_id}: {e}")
        
        logger.info(f"Scheduled message task completed for user_id: {user_id}, success: {result['success']}")
        return result
        
    except Exception as exc:
        logger.error(f"Error in scheduled message task: {exc}")
        
        # Update scheduled message status on error
        if scheduled_message_id:
            try:
                scheduled_msg = ScheduledMessage.objects.get(id=scheduled_message_id)
                scheduled_msg.status = 'failed'
                scheduled_msg.error_message = str(exc)
                scheduled_msg.save()
            except Exception as e:
                logger.error(f"Error updating scheduled message {scheduled_message_id} on failure: {e}")
        
        # Retry the task if retries are available
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying task in 60 seconds. Retry {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60, exc=exc)
        
        return {
            'success': False,
            'error': f'Task failed after {self.max_retries} retries: {str(exc)}',
            'user_id': user_id,
            'scheduled_message_id': scheduled_message_id
        }


@shared_task
def cleanup_old_scheduled_messages():
    """
    Celery task to cleanup old completed/failed scheduled messages
    Runs daily to keep the database clean
    """
    try:
        # Delete messages older than 30 days that are completed or failed
        cutoff_date = timezone.now() - timedelta(days=30)
        deleted_count = ScheduledMessage.objects.filter(
            created_at__lt=cutoff_date,
            status__in=['sent', 'failed', 'cancelled']
        ).delete()[0]
        
        logger.info(f"Cleaned up {deleted_count} old scheduled messages")
        return {'success': True, 'deleted_count': deleted_count}
        
    except Exception as exc:
        logger.error(f"Error in cleanup task: {exc}")
        return {'success': False, 'error': str(exc)}
