# Kurigram Migration Documentation

## Overview

This document describes the migration from Telethon to Kurigram for the `send-auto-message` functionality. The migration maintains all existing functionality while using the modern Kurigram library instead of Telethon.

## What Changed

### 1. New Kurigram Implementation
- **File**: `userbot/external/kurigram/message_sender.py`
- **Class**: `KurigramMessageSender`
- **Function**: `send_message_to_auto_folder(user_id, text)`

### 2. Updated Imports
- **views.py**: Changed import from Telethon to Kurigram implementation
- **tasks.py**: Updated Celery task to use Kurigram

### 3. Key Differences

#### Telethon vs Kurigram API Mapping:
- `TelegramClient` → `Client`
- `get_dialog_filters()` → `get_folders()`
- `FloodWaitError` → `FloodWait`
- `types.InputPeer*` → Direct chat objects

#### Folder Detection:
- **Telethon**: Used `GetDialogFiltersRequest()` and `include_peers`
- **<PERSON><PERSON>ram**: Uses `get_folders()` and checks `pinned_chats` + `included_chats`

#### Session Management:
- **Telethon**: File-based sessions with session names
- **Ku<PERSON>ram**: Compatible with existing session files, uses same session names

## Features Preserved

✅ **AUTO Folder Detection**: Finds chats in the "AUTO" folder
✅ **Flood Control**: Handles Telegram rate limiting with retry logic
✅ **Error Handling**: Comprehensive error reporting and logging
✅ **Async Support**: Full asynchronous operation
✅ **Session Management**: Compatible with existing session storage
✅ **API Compatibility**: Same API interface for views and tasks

## Installation

The `kurigram>=2.0.0` package is already included in `requirements.txt`.

To install:
```bash
pip install -r requirements.txt
```

## Testing

Run the migration test:
```bash
python test_kurigram_migration.py
```

This will:
1. Find an active session in the database
2. Test connection with Kurigram
3. Attempt to send a test message to AUTO folder
4. Report results

## API Usage

The API endpoint remains unchanged:

```bash
POST /api/send-auto-message/
Content-Type: application/json

{
    "user_id": 123456789,
    "text": "Your message here"
}
```

Response format is also preserved:
```json
{
    "success": true,
    "message": "Message sending completed for user_id: 123456789",
    "results": {
        "success": [...],
        "failed": [...],
        "total_chats": 5,
        "sent_count": 4,
        "failed_count": 1
    }
}
```

## Session Compatibility

The migration is designed to work with existing session data:
- Uses existing `Session` model from database
- Compatible with current session file structure
- No need to re-authenticate users

## Error Handling

Enhanced error handling includes:
- Connection failures
- AUTO folder not found
- Individual chat send failures
- Flood wait handling with automatic retry
- Comprehensive logging

## Monitoring

Check logs for:
- Connection status: `Successfully connected Kurigram client for user {user_id}`
- Folder detection: `Found {count} chats in AUTO folder for user {user_id}`
- Message sending: `Message sent to chat {chat_id} for user {user_id}`
- Errors: All errors are logged with detailed information

## Rollback Plan

If issues occur, you can quickly rollback by:

1. Revert the import changes in `views.py`:
```python
from userbot.external.telethon.message_sender import send_message_to_auto_folder
```

2. Revert the import changes in `tasks.py`:
```python
from userbot.external.telethon.message_sender import send_message_to_auto_folder
```

The Telethon implementation remains intact and functional.

## Performance Improvements

Kurigram offers several advantages:
- **Modern API**: Based on latest MTProto specifications
- **Better Error Handling**: More specific error types and handling
- **Improved Performance**: Optimized for modern Python async patterns
- **Active Development**: Regular updates and bug fixes

## Next Steps

1. **Test the migration** with the provided test script
2. **Monitor logs** for any issues during initial usage
3. **Verify functionality** with real AUTO folder messages
4. **Consider removing Telethon** dependency after successful migration (optional)

## Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify session data is correct in the database
3. Ensure AUTO folder exists and contains chats
4. Test with the provided test script

The migration maintains full backward compatibility while providing the benefits of the modern Kurigram library.
